#!/usr/bin/env node

/**
 * Test Script for Canonical URLs
 * This script tests that canonical URLs are properly rendered in the HTML
 */

const http = require('http');

const BASE_URL = 'http://localhost:3001';
const EXPECTED_DOMAIN = 'https://valueans.com';

// Test pages with their expected canonical URLs
const testPages = [
  { path: '/', expectedCanonical: ['https://valueans.com/', 'https://valueans.com'] },
  { path: '/contact', expectedCanonical: ['https://valueans.com/contact'] },
  { path: '/portfolio', expectedCanonical: ['https://valueans.com/portfolio'] },
  { path: '/AI', expectedCanonical: ['https://valueans.com/AI'] },
  { path: '/DataEngineering', expectedCanonical: ['https://valueans.com/DataEngineering'] },
];

console.log('🔍 Testing Canonical URLs in HTML...\n');

function testCanonicalUrl(path, expectedCanonical) {
  return new Promise((resolve, reject) => {
    const url = `${BASE_URL}${path}`;
    
    http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        // Look for canonical link in the HTML
        const canonicalRegex = /<link\s+rel=["']canonical["']\s+href=["']([^"']+)["']/i;
        const match = data.match(canonicalRegex);
        
        if (match) {
          const foundCanonical = match[1];
          const expectedUrls = Array.isArray(expectedCanonical) ? expectedCanonical : [expectedCanonical];

          if (expectedUrls.includes(foundCanonical)) {
            console.log(`✅ ${path} - Canonical URL correct: ${foundCanonical}`);
            resolve(true);
          } else {
            console.log(`❌ ${path} - Canonical URL mismatch:`);
            console.log(`   Expected: ${expectedUrls.join(' or ')}`);
            console.log(`   Found: ${foundCanonical}`);
            resolve(false);
          }
        } else {
          console.log(`❌ ${path} - No canonical URL found in HTML`);
          resolve(false);
        }
      });
    }).on('error', (err) => {
      console.log(`❌ ${path} - Error fetching page: ${err.message}`);
      resolve(false);
    });
  });
}

async function runTests() {
  console.log(`🎯 Testing against: ${BASE_URL}`);
  console.log(`🎯 Expected domain: ${EXPECTED_DOMAIN}\n`);
  
  let allPassed = true;
  
  for (const testPage of testPages) {
    const result = await testCanonicalUrl(testPage.path, testPage.expectedCanonical);
    if (!result) {
      allPassed = false;
    }
  }
  
  console.log('\n🏁 Canonical URL testing complete!');
  
  if (allPassed) {
    console.log('✅ All canonical URLs are working correctly!');
  } else {
    console.log('❌ Some canonical URLs need attention.');
  }
  
  console.log('\n📝 Instructions for manual verification:');
  console.log('1. Open the website in your browser');
  console.log('2. Right-click and select "Inspect" or press F12');
  console.log('3. Look for <link rel="canonical" href="..."> in the <head> section');
  console.log('4. Verify the canonical URL points to the correct valueans.com domain');
}

// Check if dev server is running
console.log('🔄 Checking if development server is running...');
http.get(BASE_URL, (res) => {
  console.log('✅ Development server is running\n');
  runTests();
}).on('error', (err) => {
  console.log('❌ Development server is not running');
  console.log('Please start the development server with: yarn dev');
  console.log('Then run this script again.');
});
