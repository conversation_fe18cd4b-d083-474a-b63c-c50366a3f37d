"use client";

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import Image from 'next/image';

export async function generateMetadata({ params }) {
  // For dynamic routes, we'll use a generic canonical URL
  // In a real implementation, you might fetch the blog data to get the actual title
  return {
    title: `Blog Post - ${params.slug} | Valueans`,
    description: "Read our latest blog post about software development, technology trends, and business insights.",
    alternates: {
      canonical: `https://valueans.com/blog/${params.slug}`,
    },
  };
}

export default function BlogPost() {
  const params = useParams();
  const [blog, setBlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        setLoading(true);
        // First, get all blogs to find the one with matching slug
        const response = await fetch("https://api.valueans.com/api/blogs/");
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Find the blog with matching slug
        const foundBlog = data.results?.find(blog => blog.slug_field === params.slug);
        
        if (!foundBlog) {
          throw new Error('Blog not found');
        }
        
        setBlog(foundBlog);
        setError(null);
      } catch (err) {
        console.error("Error fetching blog:", err);
        setError("Failed to load blog. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    if (params.slug) {
      fetchBlog();
    }
  }, [params.slug]);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">Loading blog post...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center text-red-600">{error}</div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">Blog not found</div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <article>
        {/* Blog Header */}
        <header className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">{blog.title}</h1>
          
          <div className="flex items-center gap-4 text-gray-600 mb-6">
            <span>By {blog.uploaded_by?.name || 'Unknown Author'}</span>
            <span>•</span>
            <span>{new Date(blog.created_at).toLocaleDateString()}</span>
            <span>•</span>
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
              {blog.categories?.name || 'Uncategorized'}
            </span>
          </div>

          {/* Featured Image */}
          {blog.image && (
            <div className="relative w-full h-64 md:h-96 mb-8">
              <Image
                src={blog.image}
                alt={blog.title}
                layout="fill"
                objectFit="cover"
                className="rounded-lg"
              />
            </div>
          )}
        </header>

        {/* Blog Content */}
        <div 
          className="prose prose-lg max-w-none"
          dangerouslySetInnerHTML={{ __html: blog.content }}
        />

        {/* Blog Tags */}
        {blog.tags && (
          <div className="mt-8 pt-8 border-t">
            <h3 className="text-lg font-semibold mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {blog.tags.split(',').map((tag, index) => (
                <span 
                  key={index}
                  className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                >
                  {tag.trim()}
                </span>
              ))}
            </div>
          </div>
        )}
      </article>
    </div>
  );
}
