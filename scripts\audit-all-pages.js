#!/usr/bin/env node

/**
 * Comprehensive Page Audit Script
 * This script finds all page.jsx and page.js files and checks their canonical URL status
 */

const fs = require('fs');
const path = require('path');

const EXPECTED_DOMAIN = 'https://valueans.com';

console.log('🔍 Auditing all page files for canonical URLs...\n');

function findAllPageFiles(dir, pageFiles = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (!file.startsWith('.') && file !== 'node_modules') {
        findAllPageFiles(filePath, pageFiles);
      }
    } else if (file === 'page.jsx' || file === 'page.js') {
      pageFiles.push(filePath);
    }
  }
  
  return pageFiles;
}

function getExpectedCanonicalUrl(filePath) {
  // Convert file path to URL path
  const appDir = path.join(process.cwd(), 'src', 'app');
  let urlPath = path.relative(appDir, filePath)
    .replace(/\\/g, '/') // Convert Windows backslashes to forward slashes
    .replace('/page.jsx', '') // Remove page.jsx
    .replace('/page.js', ''); // Remove page.js

  // Handle root page
  if (urlPath === '' || urlPath === '.') {
    return `${EXPECTED_DOMAIN}/`;
  }

  return `${EXPECTED_DOMAIN}/${urlPath}`;
}

function checkPageCanonicalUrl(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const expectedUrl = getExpectedCanonicalUrl(filePath);
    
    // Check if file has metadata export
    if (!content.includes('export const metadata')) {
      return {
        status: 'missing_metadata',
        filePath,
        expectedUrl,
        message: 'No metadata export found'
      };
    }
    
    // Check if metadata has alternates.canonical
    if (!content.includes('alternates:') || !content.includes('canonical:')) {
      return {
        status: 'missing_canonical',
        filePath,
        expectedUrl,
        message: 'No canonical URL in metadata'
      };
    }
    
    // Extract canonical URL from metadata (handle both quotes and template literals)
    const canonicalMatch = content.match(/canonical:\s*(?:["']([^"']+)["']|`([^`]+)`)/);
    if (canonicalMatch) {
      const foundCanonical = canonicalMatch[1] || canonicalMatch[2];
      if (foundCanonical === expectedUrl ||
          (expectedUrl.endsWith('/') && foundCanonical === expectedUrl.slice(0, -1)) ||
          (expectedUrl === `${EXPECTED_DOMAIN}/` && foundCanonical === `${EXPECTED_DOMAIN}/`)) {
        return {
          status: 'correct',
          filePath,
          expectedUrl,
          foundUrl: foundCanonical,
          message: 'Canonical URL is correct'
        };
      } else {
        return {
          status: 'incorrect',
          filePath,
          expectedUrl,
          foundUrl: foundCanonical,
          message: 'Canonical URL does not match expected'
        };
      }
    }
    
    return {
      status: 'missing_canonical',
      filePath,
      expectedUrl,
      message: 'Canonical URL not found in metadata'
    };
    
  } catch (error) {
    return {
      status: 'error',
      filePath,
      expectedUrl: getExpectedCanonicalUrl(filePath),
      message: `Error reading file: ${error.message}`
    };
  }
}

function runAudit() {
  const appDir = path.join(process.cwd(), 'src', 'app');
  const pageFiles = findAllPageFiles(appDir);
  
  console.log(`📊 Found ${pageFiles.length} page files\n`);
  
  const results = {
    correct: [],
    missing_metadata: [],
    missing_canonical: [],
    incorrect: [],
    error: []
  };
  
  pageFiles.forEach(filePath => {
    const result = checkPageCanonicalUrl(filePath);
    results[result.status].push(result);
  });
  
  // Display results
  console.log('📈 AUDIT RESULTS:\n');
  
  console.log(`✅ Correct canonical URLs: ${results.correct.length}`);
  results.correct.forEach(r => {
    console.log(`   ${r.filePath.replace(process.cwd(), '.')}`);
  });
  
  console.log(`\n❌ Missing metadata: ${results.missing_metadata.length}`);
  results.missing_metadata.forEach(r => {
    console.log(`   ${r.filePath.replace(process.cwd(), '.')} -> Expected: ${r.expectedUrl}`);
  });
  
  console.log(`\n❌ Missing canonical URLs: ${results.missing_canonical.length}`);
  results.missing_canonical.forEach(r => {
    console.log(`   ${r.filePath.replace(process.cwd(), '.')} -> Expected: ${r.expectedUrl}`);
  });
  
  console.log(`\n❌ Incorrect canonical URLs: ${results.incorrect.length}`);
  results.incorrect.forEach(r => {
    console.log(`   ${r.filePath.replace(process.cwd(), '.')}`);
    console.log(`      Expected: ${r.expectedUrl}`);
    console.log(`      Found: ${r.foundUrl}`);
  });
  
  if (results.error.length > 0) {
    console.log(`\n⚠️  Errors: ${results.error.length}`);
    results.error.forEach(r => {
      console.log(`   ${r.filePath.replace(process.cwd(), '.')}: ${r.message}`);
    });
  }
  
  const totalIssues = results.missing_metadata.length + results.missing_canonical.length + results.incorrect.length;
  
  console.log(`\n📊 SUMMARY:`);
  console.log(`   Total pages: ${pageFiles.length}`);
  console.log(`   Correct: ${results.correct.length}`);
  console.log(`   Issues: ${totalIssues}`);
  console.log(`   Coverage: ${((results.correct.length / pageFiles.length) * 100).toFixed(1)}%`);
  
  return results;
}

// Run the audit
const results = runAudit();

// Export results for potential use by other scripts
module.exports = { runAudit, results };
